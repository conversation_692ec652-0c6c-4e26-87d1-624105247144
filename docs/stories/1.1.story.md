---
docType: story
epic: 1
story: 1
title: <PERSON>hi<PERSON><PERSON> lập môi trường phát triển và Monorepo
status: Draft
---

# Story 1.1: Thiế<PERSON> lập môi trường phát triển và Monorepo

**As a** developer, **I want to** set up the monorepo structure with pnpm, **so that I can** manage frontend and backend code efficiently.

## Acceptance Criteria

- **1.1.1:** <PERSON><PERSON><PERSON> được khởi tạo với pnpm.
- **1.1.2:** Các project frontend (Next.js) và backend (Node.js/Express) được tạo trong monorepo.
- **1.1.3:** <PERSON><PERSON><PERSON> hình TypeScript được thiết lập cho cả frontend và backend.
- **1.1.4:** Các script cơ bản để chạy dev server và build được cấu hình.

## Dev Notes

### Previous Story Insights

- This is the first story, no previous insights.

### Data Models

- No specific data models are being implemented in this story. [Source: architecture/data-models.md]

### API Specifications

- No API endpoints are being implemented in this story. [Source: architecture/api-specification.md]

### Component Specifications

- No UI components are being built in this story. [Source: architecture/components.md]

### File Locations

- The project structure should be a monorepo managed by pnpm.
- The root directory will contain `pnpm-workspace.yaml`.
- Applications will be located in the `apps/` directory.
- Shared packages (UI components, types, configs) will be in the `packages/` directory.
- **`apps/web`**: Next.js 13 frontend application.
- **`apps/api`**: Node.js/Express backend application.
- **`packages/shared-types`**: For sharing TypeScript types between `web` and `api`.
- **`packages/ui`**: Shared React components (based on shadcn/ui).
- **`packages/config`**: Shared configurations (e.g., ESLint, TypeScript).
- [Source: architecture/unified-project-structure.md]

### Testing Requirements

- While this story is primarily about setup, the testing frameworks should be installed as part of the initial dependency setup.
- **Frameworks to install**: Jest, React Testing Library (for frontend), Supertest (for backend), Playwright (for E2E).
- Basic test configurations should be created for both `apps/web` and `apps/api`.
- [Source: architecture/testing-strategy.md]

### Technical Constraints & Guidance

- **Package Manager**: Use `pnpm` for workspace management. [Source: architecture/development-workflow.md]
- **Node.js Version**: Use Node.js v20+. [Source: architecture/development-workflow.md]
- **Frontend**:
  - Framework: Next.js 13.x
  - Language: TypeScript ~5.4.5
  - UI: shadcn/ui, Tailwind CSS
  - Bundler: Webpack (default for Next.js 13)
- **Backend**:
  - Framework: Node.js / Express ~4.19.2
  - Language: TypeScript ~5.4.5
- **TypeScript**: A base `tsconfig.json` should be created in the `packages/config` directory and extended by the applications in the `apps` directory to ensure consistency. [Source: architecture/coding-standards.md]
- **Git Workflow**: Use Feature Branch Workflow with Pull Requests and Code Review. [Source: architecture/development-workflow.md]

## Tasks / Subtasks

1.  **Initialize Project and pnpm Workspace (AC: 1.1.1)**

    - [ ] Initialize a new git repository.
    - [ ] Create a `pnpm-workspace.yaml` file in the root.
    - [ ] Create the directory structure: `apps/`, `packages/`. [Source: architecture/unified-project-structure.md]

2.  **Scaffold Frontend Application (AC: 1.1.2)**

    - [ ] Inside the `apps/` directory, create a new Next.js 13 project named `web`.
    - [ ] Ensure the project is initialized with TypeScript.
    - [ ] Install core frontend dependencies: `react`, `react-dom`, `next`, `typescript`, `@types/react`, `@types/node`. [Source: architecture/tech-stack.md]
    - [ ] Install and configure Tailwind CSS. [Source: architecture/tech-stack.md]
    - [ ] Install shadcn/ui and its dependencies. [Source: architecture/tech-stack.md]

3.  **Scaffold Backend Application (AC: 1.1.2)**

    - [ ] Inside the `apps/` directory, create a new Node.js project named `api`.
    - [ ] Initialize it with `pnpm init`.
    - [ ] Install core backend dependencies: `express`, `typescript`, `@types/express`, `ts-node`. [Source: architecture/tech-stack.md]
    - [ ] Create a basic Express server entry point (e.g., `src/index.ts`).

4.  **Configure Shared TypeScript and Linting (AC: 1.1.3)**

    - [ ] Create a `packages/config` directory.
    - [ ] Create a base `tsconfig.json` in `packages/config` to be shared.
    - [ ] Configure `apps/web/tsconfig.json` and `apps/api/tsconfig.json` to extend the base configuration.
    - [ ] Set up shared ESLint/Prettier configurations in `packages/config`.

5.  **Create Development Scripts (AC: 1.1.4)**

    - [ ] In the root `package.json`, add scripts to run the `web` and `api` dev servers concurrently (e.g., using `pnpm --parallel`).
    - [ ] Add build scripts for both `web` and `api` applications.
    - [ ] Ensure `pnpm install` correctly installs dependencies for all workspaces.

6.  **Setup Initial Testing Frameworks**
    - [ ] Add Jest, React Testing Library to `apps/web` and create a sample component test.
    - [ ] Add Jest, Supertest to `apps/api` and create a sample endpoint test.
    - [ ] [Source: architecture/testing-strategy.md]
