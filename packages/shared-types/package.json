{"name": "shared-types", "version": "0.0.0", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts", "dev": "tsup src/index.ts --format esm,cjs --dts --watch", "lint": "eslint \"src/**/*.ts\"", "clean": "rm -rf dist"}, "devDependencies": {"eslint": "^8.49.0", "eslint-config-custom": "workspace:*", "tsconfig": "workspace:*", "tsup": "^7.2.0", "typescript": "^5.4.5"}}