{"version": 3, "names": ["originalGetModuleName", "getModuleName", "exports", "default", "rootOpts", "pluginOpts", "_pluginOpts$moduleId", "_pluginOpts$moduleIds", "_pluginOpts$getModule", "_pluginOpts$moduleRoo", "moduleId", "moduleIds", "getModuleId", "moduleRoot", "filename", "filenameRelative", "sourceRoot", "moduleName", "sourceRootReplacer", "RegExp", "replace"], "sources": ["../src/get-module-name.ts"], "sourcesContent": ["type RootOptions = {\n  filename?: string;\n  filenameRelative?: string;\n  sourceRoot?: string;\n};\n\nexport type PluginOptions = {\n  moduleId?: string;\n  moduleIds?: boolean;\n  getModuleId?: (moduleName: string) => string | null | undefined;\n  moduleRoot?: string;\n};\n\nif (!process.env.BABEL_8_BREAKING) {\n  const originalGetModuleName = getModuleName;\n\n  // @ts-expect-error TS doesn't like reassigning a function.\n  getModuleName = function getModuleName(\n    rootOpts: RootOptions & PluginOptions,\n    pluginOpts: PluginOptions,\n  ): string | null {\n    return originalGetModuleName(rootOpts, {\n      moduleId: pluginOpts.moduleId ?? rootOpts.moduleId,\n      moduleIds: pluginOpts.moduleIds ?? rootOpts.moduleIds,\n      getModuleId: pluginOpts.getModuleId ?? rootOpts.getModuleId,\n      moduleRoot: pluginOpts.moduleRoot ?? rootOpts.moduleRoot,\n    });\n  };\n}\n\nexport default function getModuleName(\n  rootOpts: RootOptions,\n  pluginOpts: PluginOptions,\n): string | null {\n  const {\n    filename,\n    filenameRelative = filename,\n    sourceRoot = pluginOpts.moduleRoot,\n  } = rootOpts;\n\n  const {\n    moduleId,\n    moduleIds = !!moduleId,\n\n    getModuleId,\n\n    moduleRoot = sourceRoot,\n  } = pluginOpts;\n\n  if (!moduleIds) return null;\n\n  // moduleId is n/a if a `getModuleId()` is provided\n  if (moduleId != null && !getModuleId) {\n    return moduleId;\n  }\n\n  let moduleName = moduleRoot != null ? moduleRoot + \"/\" : \"\";\n\n  if (filenameRelative) {\n    const sourceRootReplacer =\n      sourceRoot != null ? new RegExp(\"^\" + sourceRoot + \"/?\") : \"\";\n\n    moduleName += filenameRelative\n      // remove sourceRoot from filename\n      .replace(sourceRootReplacer, \"\")\n      // remove extension\n      .replace(/\\.\\w*$/, \"\");\n  }\n\n  // normalize path separators\n  moduleName = moduleName.replace(/\\\\/g, \"/\");\n\n  if (getModuleId) {\n    // If return is falsy, assume they want us to use our generated default name\n    return getModuleId(moduleName) || moduleName;\n  } else {\n    return moduleName;\n  }\n}\n"], "mappings": ";;;;;;AAamC;EACjC,MAAMA,qBAAqB,GAAGC,aAAa;EAG3CC,OAAA,CAAAC,OAAA,GAAAF,aAAa,GAAG,SAASA,aAAaA,CACpCG,QAAqC,EACrCC,UAAyB,EACV;IAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACf,OAAOT,qBAAqB,CAACI,QAAQ,EAAE;MACrCM,QAAQ,GAAAJ,oBAAA,GAAED,UAAU,CAACK,QAAQ,YAAAJ,oBAAA,GAAIF,QAAQ,CAACM,QAAQ;MAClDC,SAAS,GAAAJ,qBAAA,GAAEF,UAAU,CAACM,SAAS,YAAAJ,qBAAA,GAAIH,QAAQ,CAACO,SAAS;MACrDC,WAAW,GAAAJ,qBAAA,GAAEH,UAAU,CAACO,WAAW,YAAAJ,qBAAA,GAAIJ,QAAQ,CAACQ,WAAW;MAC3DC,UAAU,GAAAJ,qBAAA,GAAEJ,UAAU,CAACQ,UAAU,YAAAJ,qBAAA,GAAIL,QAAQ,CAACS;IAChD,CAAC,CAAC;EACJ,CAAC;AACH;AAEe,SAASZ,aAAaA,CACnCG,QAAqB,EACrBC,UAAyB,EACV;EACf,MAAM;IACJS,QAAQ;IACRC,gBAAgB,GAAGD,QAAQ;IAC3BE,UAAU,GAAGX,UAAU,CAACQ;EAC1B,CAAC,GAAGT,QAAQ;EAEZ,MAAM;IACJM,QAAQ;IACRC,SAAS,GAAG,CAAC,CAACD,QAAQ;IAEtBE,WAAW;IAEXC,UAAU,GAAGG;EACf,CAAC,GAAGX,UAAU;EAEd,IAAI,CAACM,SAAS,EAAE,OAAO,IAAI;EAG3B,IAAID,QAAQ,IAAI,IAAI,IAAI,CAACE,WAAW,EAAE;IACpC,OAAOF,QAAQ;EACjB;EAEA,IAAIO,UAAU,GAAGJ,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAG,GAAG,GAAG,EAAE;EAE3D,IAAIE,gBAAgB,EAAE;IACpB,MAAMG,kBAAkB,GACtBF,UAAU,IAAI,IAAI,GAAG,IAAIG,MAAM,CAAC,GAAG,GAAGH,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE;IAE/DC,UAAU,IAAIF,gBAAgB,CAE3BK,OAAO,CAACF,kBAAkB,EAAE,EAAE,CAAC,CAE/BE,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;EAC1B;EAGAH,UAAU,GAAGA,UAAU,CAACG,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EAE3C,IAAIR,WAAW,EAAE;IAEf,OAAOA,WAAW,CAACK,UAAU,CAAC,IAAIA,UAAU;EAC9C,CAAC,MAAM;IACL,OAAOA,UAAU;EACnB;AACF", "ignoreList": []}