'use client';

import Image from 'next/image';
import Link from 'next/link';
import { Heart, ShoppingCart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { StarRating } from '@/components/ui/star-rating';
import { formatCurrency, calculateDiscount } from '@/lib/utils/currency';
import { Product } from '@/lib/mock/products';

interface ProductCardProps {
  product: Product;
  variant?: 'default' | 'featured';
  className?: string;
}

export function ProductCard({ product, variant = 'default', className = '' }: ProductCardProps) {
  const isOnSale = product.discountPrice && product.discountPrice < product.price;
  const discount = isOnSale ? calculateDiscount(product.price, product.discountPrice) : 0;

  return (
    <Card className={`group relative overflow-hidden transition-all duration-300 hover:shadow-lg ${className}`}>
      <div className="aspect-square relative overflow-hidden bg-gray-50">
        <Link href={`/product/${product.slug}`}>
          <Image
            src={product.images[0]}
            alt={product.name}
            fill
            className="object-cover transition-transform duration-500 group-hover:scale-105"
            sizes="(min-width: 1024px) 25vw, (min-width: 768px) 33vw, (min-width: 640px) 50vw, 100vw"
          />
        </Link>
        
        {/* Badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-1">
          {product.isNew && (
            <Badge variant="default" className="bg-blue-600 hover:bg-blue-700">
              New
            </Badge>
          )}
          {isOnSale && (
            <Badge variant="destructive">
              -{discount}%
            </Badge>
          )}
        </div>

        {/* Quick Actions */}
        <div className="absolute top-2 right-2 flex flex-col gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <Button
            size="icon"
            variant="secondary"
            className="h-8 w-8 bg-white/90 hover:bg-white"
            aria-label={`Add ${product.name} to wishlist`}
          >
            <Heart className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <CardContent className="p-3">
        <div className="space-y-2">
          {/* Brand */}
          <p className="text-xs text-muted-foreground uppercase tracking-wide">
            {product.brand}
          </p>

          {/* Product Name */}
          <Link 
            href={`/product/${product.slug}`}
            className="block hover:underline"
          >
            <h3 className="font-medium text-sm leading-tight line-clamp-2 min-h-[2.5rem]">
              {product.name}
            </h3>
          </Link>

          {/* Rating */}
          <div className="flex items-center gap-1">
            <StarRating rating={product.rating} size="sm" />
            <span className="text-xs text-muted-foreground">
              ({product.reviewCount})
            </span>
          </div>

          {/* Price */}
          <div className="flex items-baseline gap-2">
            {isOnSale ? (
              <>
                <span className="font-semibold text-sm">
                  {formatCurrency(product.discountPrice!)}
                </span>
                <span className="text-xs text-muted-foreground line-through">
                  {formatCurrency(product.price)}
                </span>
              </>
            ) : (
              <span className="font-semibold text-sm">
                {formatCurrency(product.price)}
              </span>
            )}
          </div>

          {/* Quick Add to Cart */}
          <Button
            size="sm"
            className="w-full mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            asChild
          >
            <Link href={`/product/${product.slug}`}>
              <ShoppingCart className="h-4 w-4 mr-1" />
              Quick View
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}